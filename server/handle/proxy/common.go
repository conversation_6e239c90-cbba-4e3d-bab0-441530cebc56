package proxy

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/jsonrpc"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpclient"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpserver"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// PointsCheckResult 积分检查结果
type PointsCheckResult struct {
	ShouldDeduct bool
	ToolID       string
	UserID       uint64
	ToolRecord   mcp.ProjectTools
	ErrorType    string // 错误类型：如 "insufficient_points", "tool_not_found", "user_not_found"
	ErrorMessage string // 详细错误信息
}

// ThirdPartyProxyConfig 第三方代理配置
type ThirdPartyProxyConfig struct {
	URL         string
	Headers     http.Header
	RequestBody []byte
	Timeout     time.Duration
}

// ProcessReuseConfig 进程复用配置
type ProcessReuseConfig struct {
	ShareProcess bool
	Key          string
	ServerConfig interface{} // mcpserver.ServerConfig
	Context      *proxy.SSEContext
	Session      *proxy.SSESession
}

// validatePointsForToolCall 验证工具调用的积分
func validatePointsForToolCall(ctx context.Context, request *jsonrpc.Request, serverUUID, userIDStr string) (*PointsCheckResult, error) {
	result := &PointsCheckResult{}

	log.Printf("[validatePointsForToolCall] 开始验证: request=%+v, serverUUID=%s, userIDStr=%s", request, serverUUID, userIDStr)

	if request == nil || request.Method != "tools/call" {
		log.Printf("[validatePointsForToolCall] 非tools/call请求，跳过: method=%s", request.Method)
		return result, nil
	}

	// 提取工具名称
	if paramsMap, ok := request.Params.(map[string]interface{}); ok {
		if name, ok := paramsMap["name"].(string); ok {
			result.ToolID = name
			log.Printf("[validatePointsForToolCall] 提取到工具名称: %s", name)
		} else {
			log.Printf("[validatePointsForToolCall] 参数中没有name字段: params=%+v", paramsMap)
		}
	} else {
		log.Printf("[validatePointsForToolCall] 参数不是map类型: params=%+v", request.Params)
	}

	if userIDStr == "" || result.ToolID == "" {
		log.Printf("[validatePointsForToolCall] 缺少必要参数: userIDStr=%s, ToolID=%s", userIDStr, result.ToolID)
		return result, nil
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil || userID == 0 {
		return result, nil
	}
	result.UserID = userID

	// 查询项目
	var project mcp.Projects
	if err := global.GVA_DB.Where("uuid = ?", serverUUID).First(&project).Error; err != nil {
		result.ErrorType = "project_not_found"
		result.ErrorMessage = "项目不存在"
		return result, nil
	}

	// 查询工具
	if err := global.GVA_DB.Where("project_id = ? AND name = ?", project.ID, result.ToolID).First(&result.ToolRecord).Error; err != nil {
		result.ErrorType = "tool_not_found"
		result.ErrorMessage = "工具不存在"
		return result, nil
	}

	// 查询用户
	var user system.SysUser
	if err := global.GVA_DB.Where("id = ?", userID).First(&user).Error; err != nil {
		result.ErrorType = "user_not_found"
		result.ErrorMessage = "用户不存在"
		return result, nil
	}

	// 检查积分是否足够
	if result.ToolRecord.Points != nil && user.FreePoints+user.Points < *result.ToolRecord.Points {
		result.ErrorType = "insufficient_points"
		result.ErrorMessage = fmt.Sprintf("积分不足，需要%d积分，当前有%d积分", *result.ToolRecord.Points, user.FreePoints+user.Points)
		log.Printf("[validatePointsForToolCall] 积分不足: 需要=%d, 当前=%d", *result.ToolRecord.Points, user.FreePoints+user.Points)
		return result, nil
	}

	result.ShouldDeduct = true
	log.Printf("[validatePointsForToolCall] 验证成功: ToolID=%s, Points=%v, ShouldDeduct=%t", result.ToolID, result.ToolRecord.Points, result.ShouldDeduct)
	return result, nil
}

// deductPointsAfterToolCall 工具调用后扣除积分
func deductPointsAfterToolCall(ctx context.Context, checkResult *PointsCheckResult, response interface{}) error {
	log.Printf("[deductPointsAfterToolCall] 开始扣积分检查: ShouldDeduct=%t, UserID=%d, ToolID=%s", checkResult.ShouldDeduct, checkResult.UserID, checkResult.ToolID)

	if !checkResult.ShouldDeduct || checkResult.UserID == 0 || checkResult.ToolID == "" {
		log.Printf("[deductPointsAfterToolCall] 跳过扣积分: ShouldDeduct=%t, UserID=%d, ToolID=%s", checkResult.ShouldDeduct, checkResult.UserID, checkResult.ToolID)
		return nil
	}

	// 检查响应是否有错误
	shouldDeduct := true
	if response != nil {
		if resultBytes, err := json.Marshal(response); err == nil {
			var callResult map[string]interface{}
			if err := json.Unmarshal(resultBytes, &callResult); err == nil {
				// 检查result字段中的isError
				if resultData, ok := callResult["result"].(map[string]interface{}); ok {
					if isError, exists := resultData["isError"]; exists && isError == true {
						shouldDeduct = false
						log.Printf("工具调用有错误，不扣积分: userID=%d, toolID=%s", checkResult.UserID, checkResult.ToolID)
					}
				}
				// 也检查顶级的isError字段
				if isError, exists := callResult["isError"]; exists && isError == true {
					shouldDeduct = false
					log.Printf("工具调用有错误，不扣积分: userID=%d, toolID=%s", checkResult.UserID, checkResult.ToolID)
				}
			}
		}
	}

	if shouldDeduct {
		err := service.ServiceGroupApp.McpServiceGroup.ProjectToolsService.UseToolWithPoints(ctx, uint(checkResult.UserID), fmt.Sprintf("%d", checkResult.ToolRecord.ID))
		if err != nil {
			log.Printf("扣积分失败: %v", err)
			return err
		} else {
			log.Printf("扣积分成功: userID=%d, toolID=%s", checkResult.UserID, checkResult.ToolID)
		}
	}

	return nil
}

// createServerLogAndUpdateUsage 创建服务器日志并更新使用计数
func createServerLogAndUpdateUsage(proxyInfo *proxy.ProxyInfo) error {
	// 只为tools/call创建日志
	if proxyInfo.RequestMethod != "tools/call" {
		return nil
	}

	// 记录调用类型（成功/失败）
	logType := "成功"
	if proxyInfo.ResponseError != "" {
		logType = "失败"
	}
	log.Printf("[ServerLog] 记录%s的%s调用: method=%s, error=%s", logType, proxyInfo.RequestMethod, proxyInfo.RequestMethod, proxyInfo.ResponseError)

	// 创建服务器日志
	serverLog := proxyInfo.ToServerLog()
	if err := mcprouter.CreateServerLog(serverLog); err != nil {
		log.Printf("save server log failed: %v\n", err)
		return err
	} else {
		log.Printf("save server log ok: %v\n", proxyInfo.RequestID)

		// 更新 projects 表的 online_usage_count 字段
		if err := global.GVA_DB.Model(&mcp.Projects{}).
			Where("uuid = ?", proxyInfo.ServerUUID).
			UpdateColumn("online_usage_count", gorm.Expr("online_usage_count + ?", 1)).
			Error; err != nil {
			log.Printf("update project online_usage_count failed: %v\n", err)
			return err
		} else {
			log.Printf("update project online_usage_count ok: %v\n", proxyInfo.ServerUUID)
		}
	}

	return nil
}

// proxyToThirdPartySSE 代理到第三方SSE服务
func proxyToThirdPartySSE(c echo.Context, config *ThirdPartyProxyConfig, session *proxy.SSESession) error {
	client := &http.Client{Timeout: config.Timeout}
	if config.Timeout == 0 {
		client.Timeout = 0 // 无超时
	}

	reqProxy, err := http.NewRequest("GET", config.URL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	reqProxy.Header = config.Headers
	for k, v := range c.Request().Header {
		if len(v) > 0 {
			reqProxy.Header.Set(k, v[0])
		}
	}

	resp, err := client.Do(reqProxy)
	if err != nil {
		return fmt.Errorf("SSE代理失败: %v", err)
	}
	defer resp.Body.Close()

	c.Response().Header().Set("Content-Type", "text/event-stream")
	scanner := bufio.NewScanner(resp.Body)
	var lastEvent string

	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "event:") {
			lastEvent = strings.TrimSpace(strings.TrimPrefix(line, "event:"))
			c.Response().Write([]byte(line + "\n"))
		} else if strings.HasPrefix(line, "data:") && lastEvent == "endpoint" {
			data := strings.TrimSpace(strings.TrimPrefix(line, "data:"))
			u, _ := url.Parse(config.URL)
			host := u.Scheme + "://" + u.Host
			finalUrl := host + data
			if session != nil {
				session.ThirdPartyMessagesURL = finalUrl
			}
			log.Printf("最终代理到第三方的URL: %s", finalUrl)
			// 返回本地消息通道给前端
			if session != nil {
				newData := "/messages?sessionid=" + session.ProxyInfo().SessionID
				c.Response().Write([]byte("data: " + newData + "\n"))
			} else {
				c.Response().Write([]byte(line + "\n"))
			}
			lastEvent = ""
		} else {
			c.Response().Write([]byte(line + "\n"))
		}
		c.Response().Flush()
	}

	return nil
}

// proxyToThirdPartyHTTP 代理到第三方HTTP服务
func proxyToThirdPartyHTTP(c echo.Context, config *ThirdPartyProxyConfig) ([]byte, error) {
	client := &http.Client{
		Timeout: config.Timeout,
		// 禁用自动解压缩以避免乱码问题
		Transport: &http.Transport{
			DisableCompression: true,
		},
	}

	reqProxy, err := http.NewRequest("POST", config.URL, bytes.NewReader(config.RequestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	reqProxy.Header = config.Headers
	// 明确指定不接受压缩
	reqProxy.Header.Set("Accept-Encoding", "identity")

	resp, err := client.Do(reqProxy)
	if err != nil {
		log.Printf("[HTTP代理] 请求失败: %v", err)
		return nil, fmt.Errorf("HTTP代理失败: %v", err)
	}
	defer resp.Body.Close()

	// 记录响应状态和头部信息
	log.Printf("[HTTP代理] 响应状态: %d, Content-Type: %s, Content-Length: %s",
		resp.StatusCode, resp.Header.Get("Content-Type"), resp.Header.Get("Content-Length"))

	// 检查HTTP状态码 - 允许200和202状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		log.Printf("[HTTP代理] 收到错误状态码: %d", resp.StatusCode)
		return nil, fmt.Errorf("上游服务返回错误: %d", resp.StatusCode)
	}

	// 读取原始响应体
	rawResult, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[HTTP代理] 读取响应体失败: %v", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应是否为空
	if len(rawResult) == 0 {
		log.Printf("[HTTP代理] 收到空响应，这在MCP协议中是正常的")
		return []byte("{}"), nil
	}

	return rawResult, nil
}

// getOrCreateMCPClient 获取或创建MCP客户端（进程复用逻辑）
func getOrCreateMCPClient(ctx *proxy.SSEContext, key string, serverConfig interface{}, session *proxy.SSESession) (mcpclient.Client, error) {
	// 这里需要类型断言，因为我们传入的是interface{}
	var config *mcpserver.ServerConfig
	switch sc := serverConfig.(type) {
	case *mcpserver.ServerConfig:
		config = sc
	default:
		return nil, fmt.Errorf("invalid server config type")
	}

	log.Printf("[进程复用] ShareProcess=%t, key=%s", config.ShareProcess, key)

	var client mcpclient.Client
	var err error

	if config.ShareProcess {
		// 复用已有 client/进程
		client = ctx.GetClient(key)
		if client == nil {
			// 检查session中是否已有客户端
			if session != nil && session.Client() != nil {
				client = session.Client()
				ctx.StoreClient(key, client)
				log.Printf("[进程复用] 从session复用客户端: key=%s", key)
			} else {
				// 创建新客户端
				client, err = mcpclient.NewClient(config)
				if err != nil {
					return nil, fmt.Errorf("connect to mcp server failed: %v", err)
				}
				if err := client.Error(); err != nil {
					return nil, fmt.Errorf("mcp server run failed: %v", err)
				}
				ctx.StoreClient(key, client)
				if session != nil {
					session.SetClient(client)
				}
				log.Printf("[进程复用] 创建新客户端并存储: key=%s", key)

				// 设置通知处理器
				if session != nil {
					client.OnNotification(func(message []byte) {
						fmt.Printf("received notification: %s\n", message)
						session.SendMessage(string(message))
					})
				} else {
					client.OnNotification(func(message []byte) {
						fmt.Printf("received notification: %s\n", message)
					})
				}
			}
		} else {
			log.Printf("[进程复用] 复用已有客户端: key=%s", key)
		}
	} else {
		// 不复用，每次都新建
		log.Printf("[进程复用] ShareProcess=false，创建新客户端: key=%s", key)
		client, err = mcpclient.NewClient(config)
		if err != nil {
			return nil, fmt.Errorf("connect to mcp server failed: %v", err)
		}
		if err := client.Error(); err != nil {
			return nil, fmt.Errorf("mcp server run failed: %v", err)
		}

		// 设置通知处理器
		if session != nil {
			client.OnNotification(func(message []byte) {
				fmt.Printf("received notification: %s\n", message)
				session.SendMessage(string(message))
			})
		} else {
			client.OnNotification(func(message []byte) {
				fmt.Printf("received notification: %s\n", message)
			})
		}
		// 不存储到 ctx.StoreClient
	}

	return client, nil
}

// handleClientError 处理客户端错误
func handleClientError(ctx *proxy.SSEContext, key string, serverConfig interface{}, session *proxy.SSESession, err error) {
	// 类型断言
	var config *mcpserver.ServerConfig
	switch sc := serverConfig.(type) {
	case *mcpserver.ServerConfig:
		config = sc
	default:
		log.Printf("invalid server config type in handleClientError")
		return
	}

	log.Printf("forward message failed: %v\n", err)

	// 只有在ShareProcess=false或者客户端确实出错时才关闭
	if !config.ShareProcess {
		if session != nil {
			session.Close()
		}
	} else {
		// ShareProcess=true时，清理存储的客户端，但不关闭session
		ctx.DeleteClient(key)
		if session != nil {
			session.SetClient(nil)
		}
		log.Printf("[进程复用] 清理出错的共享客户端: key=%s", key)
	}
}

// parseEnvAndHeaders 解析环境变量和请求头
func parseEnvAndHeaders(serverkey *mcprouter.Serverkey, requestHeaders http.Header) (map[string]string, []string, map[string]string) {
	// 解析数据库env_json，强制key大写并保留顺序
	dbEnv := map[string]string{}
	var envOrder []string
	if serverkey.EnvJson != "" {
		var rawEnv map[string]interface{}
		_ = json.Unmarshal([]byte(serverkey.EnvJson), &rawEnv)
		for k, v := range rawEnv {
			dbEnv[strings.ToUpper(k)] = fmt.Sprintf("%v", v)
		}
		// 用Decoder保留顺序
		decoder := json.NewDecoder(strings.NewReader(serverkey.EnvJson))
		_, _ = decoder.Token() // 跳过{
		for decoder.More() {
			tk, _ := decoder.Token()
			k := tk.(string)
			envOrder = append(envOrder, strings.ToUpper(k))
			decoder.Token() // 跳过值
		}
	}

	// 合并header，key不区分大小写
	normalizedHeader := map[string]string{}
	for k, v := range requestHeaders {
		if len(v) > 0 {
			normalizedHeader[strings.ToUpper(k)] = v[0]
		}
	}

	return dbEnv, envOrder, normalizedHeader
}

// shouldDeductPoints 判断是否应该扣积分
func shouldDeductPoints(serverkey *mcprouter.Serverkey, normalizedHeader, dbEnv map[string]string) bool {
	// 获取key_param_name
	keyParamName := serverkey.KeyParamName
	if keyParamName == "" {
		keyParamName = "key"
	}
	keyParamName = strings.ToUpper(keyParamName)

	// 获取header和env_json里的key值
	headerKeyValue := normalizedHeader[keyParamName]
	envKeyValue := dbEnv[keyParamName]

	// 判断是否扣积分
	deductPoints := true
	if headerKeyValue != "" && headerKeyValue != envKeyValue {
		deductPoints = false // 用户自带key，不扣积分
	}

	return deductPoints
}

// getClientIP 获取客户端真实IP地址
func getClientIP(c echo.Context) string {
	// 优先从 X-Forwarded-For 获取
	if xff := c.Request().Header.Get("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// 从 X-Real-IP 获取
	if xri := c.Request().Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// 从 RemoteAddr 获取
	if remoteAddr := c.Request().RemoteAddr; remoteAddr != "" {
		// RemoteAddr 格式通常是 "IP:Port"，需要提取IP部分
		if idx := strings.LastIndex(remoteAddr, ":"); idx != -1 {
			return remoteAddr[:idx]
		}
		return remoteAddr
	}

	return "unknown"
}

// createErrorProxyInfo 创建用于错误记录的完整ProxyInfo
func createErrorProxyInfo(c echo.Context, serverkey *mcprouter.Serverkey, serverConfig *mcpserver.ServerConfig, checkResult *PointsCheckResult, errorMessage string) *proxy.ProxyInfo {
	now := time.Now()

	// 基础信息
	proxyInfo := &proxy.ProxyInfo{
		ConnectionTime:     now,
		RequestTime:        now,
		ResponseTime:       now,
		CostTime:           0,
		RequestMethod:      "tools/call",
		ResponseError:      errorMessage,
		Points:             0, // 错误时积分记为0
		IP:                 getClientIP(c), // 获取客户端IP
	}

	// 服务器相关信息
	if serverkey != nil {
		proxyInfo.ServerKey = serverkey.ServerKey
		proxyInfo.ServerUUID = serverkey.ServerUUID
	}

	if serverConfig != nil {
		proxyInfo.ServerConfigName = serverConfig.ServerName
		proxyInfo.ServerShareProcess = serverConfig.ShareProcess
		proxyInfo.ServerType = serverConfig.ServerType
		proxyInfo.ServerURL = serverConfig.ServerURL
		proxyInfo.ServerCommand = serverConfig.Command
		proxyInfo.ServerCommandHash = serverConfig.CommandHash
		proxyInfo.ServerName = serverConfig.ServerName
		// ServerVersion 字段在 ServerConfig 中不存在，使用默认值
		proxyInfo.ServerVersion = "unknown"
	}

	// 请求相关信息
	if c != nil {
		header := c.Request().Header
		proxyInfo.RequestID = header.Get("X-Request-ID")
		proxyInfo.RequestFrom = header.Get("X-Request-From")
		proxyInfo.JSONRPCVersion = "2.0" // 默认版本
		proxyInfo.ProtocolVersion = header.Get("Mcp-Protocol-Version")
		if proxyInfo.ProtocolVersion == "" {
			proxyInfo.ProtocolVersion = "2025-03-26" // 默认协议版本
		}

		// 尝试从User-Agent解析客户端信息
		userAgent := header.Get("User-Agent")
		if userAgent != "" {
			proxyInfo.ClientName = userAgent
			proxyInfo.ClientVersion = "unknown"
		}
	}

	// 工具和用户信息
	if checkResult != nil {
		proxyInfo.ToolName = checkResult.ToolID
		proxyInfo.UserID = fmt.Sprintf("%d", checkResult.UserID)

		// 尝试解析请求参数
		if c != nil {
			ctx := proxy.GetSSEContext(c)
			if ctx != nil {
				if request, err := ctx.GetJSONRPCRequest(); err == nil && request != nil {
					proxyInfo.RequestParams = request.Params
					if request.ID != nil {
						proxyInfo.RequestID = fmt.Sprintf("%v", request.ID)
					}
				}
			}
		}
	}

	// 生成SessionID（如果没有的话）
	if proxyInfo.SessionID == "" {
		proxyInfo.SessionID = uuid.New().String()
	}

	return proxyInfo
}

// createSuccessProxyInfo 创建用于成功记录的完整ProxyInfo
func createSuccessProxyInfo(c echo.Context, serverkey *mcprouter.Serverkey, serverConfig *mcpserver.ServerConfig, checkResult *PointsCheckResult, response interface{}) *proxy.ProxyInfo {
	now := time.Now()

	// 基础信息
	proxyInfo := &proxy.ProxyInfo{
		ConnectionTime:     now,
		RequestTime:        now,
		ResponseTime:       now,
		CostTime:           0,
		RequestMethod:      "tools/call",
		ResponseResult:     response,
		Points:             0, // 默认积分为0
		IP:                 getClientIP(c), // 获取客户端IP
	}

	// 服务器相关信息
	if serverkey != nil {
		proxyInfo.ServerKey = serverkey.ServerKey
		proxyInfo.ServerUUID = serverkey.ServerUUID
	}

	if serverConfig != nil {
		proxyInfo.ServerConfigName = serverConfig.ServerName
		proxyInfo.ServerShareProcess = serverConfig.ShareProcess
		proxyInfo.ServerType = serverConfig.ServerType
		proxyInfo.ServerURL = serverConfig.ServerURL
		proxyInfo.ServerCommand = serverConfig.Command
		proxyInfo.ServerCommandHash = serverConfig.CommandHash
		proxyInfo.ServerName = serverConfig.ServerName
		// ServerVersion 字段在 ServerConfig 中不存在，使用默认值
		proxyInfo.ServerVersion = "unknown"
	}

	// 请求相关信息
	if c != nil {
		header := c.Request().Header
		proxyInfo.RequestID = header.Get("X-Request-ID")
		proxyInfo.RequestFrom = header.Get("X-Request-From")
		proxyInfo.JSONRPCVersion = "2.0" // 默认版本
		proxyInfo.ProtocolVersion = header.Get("Mcp-Protocol-Version")
		if proxyInfo.ProtocolVersion == "" {
			proxyInfo.ProtocolVersion = "2025-03-26" // 默认协议版本
		}

		// 尝试从User-Agent解析客户端信息
		userAgent := header.Get("User-Agent")
		if userAgent != "" {
			proxyInfo.ClientName = userAgent
			proxyInfo.ClientVersion = "unknown"
		}
	}

	// 工具和用户信息
	if checkResult != nil {
		proxyInfo.ToolName = checkResult.ToolID
		proxyInfo.UserID = fmt.Sprintf("%d", checkResult.UserID)
		if checkResult.ToolRecord.Points != nil {
			proxyInfo.Points = *checkResult.ToolRecord.Points
		}

		// 尝试解析请求参数
		if c != nil {
			ctx := proxy.GetSSEContext(c)
			if ctx != nil {
				if request, err := ctx.GetJSONRPCRequest(); err == nil && request != nil {
					proxyInfo.RequestParams = request.Params
					if request.ID != nil {
						proxyInfo.RequestID = fmt.Sprintf("%v", request.ID)
					}
				}
			}
		}
	}

	// 生成SessionID（如果没有的话）
	if proxyInfo.SessionID == "" {
		proxyInfo.SessionID = uuid.New().String()
	}

	return proxyInfo
}

// handlePointsCheckFailure 处理积分检查失败的情况并记录server_logs
func handlePointsCheckFailure(ctx context.Context, checkResult *PointsCheckResult, c echo.Context, serverkey *mcprouter.Serverkey, serverConfig *mcpserver.ServerConfig) error {
	if checkResult.ErrorType == "" {
		return nil // 没有错误
	}

	// 创建完整的错误ProxyInfo
	proxyInfo := createErrorProxyInfo(c, serverkey, serverConfig, checkResult, checkResult.ErrorMessage)

	// 记录失败的server_logs
	if err := createServerLogAndUpdateUsage(proxyInfo); err != nil {
		log.Printf("[积分检查] 创建失败日志记录失败: %v", err)
	}

	log.Printf("[积分检查] %s: userID=%d, toolID=%s, error=%s",
		checkResult.ErrorType, checkResult.UserID, checkResult.ToolID, checkResult.ErrorMessage)

	return fmt.Errorf(checkResult.ErrorMessage)
}

// handleThirdPartySSEProxy 处理第三方SSE代理
func handleThirdPartySSEProxy(c echo.Context, ctx *proxy.SSEContext, key string, serverkey *mcprouter.Serverkey, queryKey, pathToken, userIdStr string) error {
	// 重新获取serverConfig
	serverConfig := mcpserver.GetServerConfig(key)
	if serverConfig == nil {
		return c.String(http.StatusBadRequest, "Invalid server config")
	}

	// 解析环境变量和请求头
	dbEnv, _, normalizedHeader := parseEnvAndHeaders(serverkey, c.Request().Header)

	// 构建请求头
	headers := http.Header{}
	if serverkey.EnvJson != "" {
		var env map[string]interface{}
		_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
		for k, v := range env {
			headers.Set(k, fmt.Sprintf("%v", v))
		}
	}

	// 创建SSE writer
	writer, err := proxy.NewSSEWriter(c)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}

	// 生成session ID
	sessionID := uuid.New().String()
	proxyInfo := &proxy.ProxyInfo{
		ServerKey:          key,
		ConnectionTime:     time.Now(),
		SessionID:          sessionID,
		ServerUUID:         serverConfig.ServerUUID,
		ServerConfigName:   serverConfig.ServerName,
		ServerShareProcess: serverConfig.ShareProcess,
		ServerType:         serverConfig.ServerType,
		ServerURL:          serverConfig.ServerURL,
		ServerCommand:      serverConfig.Command,
		ServerCommandHash:  serverConfig.CommandHash,
		UserID:             userIdStr,
		HeaderEnv:          normalizedHeader,
		DbEnv:              dbEnv,
		IP:                 getClientIP(c),
	}

	// 创建session
	session := proxy.NewSSESession(writer, serverConfig, proxyInfo)
	remoteMessagesUrl := strings.Replace(serverkey.SseUrl, "/sse", "/message", 1)
	session.ThirdPartyMessagesURL = remoteMessagesUrl
	ctx.StoreSession(sessionID, session)
	defer ctx.DeleteSession(sessionID)

	// 构建完整的SSE URL
	fullSseUrl, err := buildFullURL(serverkey.SseUrl, c.Request().URL.RawQuery, queryKey, pathToken)
	if err != nil {
		log.Printf("[SSE代理] URL构建失败: %v", err)
		return c.String(http.StatusBadRequest, "Invalid SSE URL")
	}
	log.Println("[SSE代理] fullSseUrl:", fullSseUrl)

	// 创建代理配置
	config := &ThirdPartyProxyConfig{
		URL:     fullSseUrl,
		Headers: headers,
		Timeout: 0, // 无超时
	}

	// 执行代理
	return proxyToThirdPartySSE(c, config, session)
}
