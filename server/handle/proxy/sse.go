package proxy

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpserver"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// buildFullURL 构建完整的URL，正确合并查询参数
func buildFullURL(baseURL string, requestQuery string, queryKey, pathToken string) (string, error) {
	// 解析基础URL
	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	// 获取基础URL中已有的查询参数
	baseQuery := u.Query()

	// 解析请求中的查询参数
	requestQueryValues, _ := url.ParseQuery(requestQuery)

	// 合并查询参数：基础URL的参数优先，然后是请求中的参数
	for key, values := range requestQueryValues {
		if len(values) > 0 && baseQuery.Get(key) == "" {
			baseQuery.Set(key, values[0])
		}
	}

	// 添加额外的key参数（如果不存在）
	if baseQuery.Get("key") == "" && queryKey != "" {
		baseQuery.Set("key", queryKey)
	}

	// 只有当原始请求中包含token查询参数时，才添加token
	// 如果token是从路径参数来的，不要添加到查询参数中
	if baseQuery.Get("token") == "" && requestQueryValues.Get("token") != "" {
		baseQuery.Set("token", requestQueryValues.Get("token"))
	}

	// 设置合并后的查询参数
	u.RawQuery = baseQuery.Encode()

	return u.String(), nil
}

// SSE is a handler for the sse endpoint
func SSE(c echo.Context) error {
	// log.Printf("收到的header: %+v", c.Request().Header)

	ctx := proxy.GetSSEContext(c)
	if ctx == nil {
		return c.String(http.StatusInternalServerError, "Failed to get SSE context")
	}

	req := c.Request()

	// 兼容query、form、路径参数
	queryKey := c.QueryParam("key")
	queryToken := c.QueryParam("token")
	pathKey := c.Param("key")
	pathToken := c.Param("token")

	key := pathKey // 查 serverkey、serverConfig 用 pathKey
	token := queryToken
	if token == "" {
		token = pathToken
	}

	// 获取serverkey信息，用于获取KeyParamName
	serverkey, errKey := mcprouter.FindServerkeyByServerKey(key)
	if errKey != nil {
		serverkey = &mcprouter.Serverkey{KeyParamName: "key"}
	}

	// 验证 token 并获取 user_id
	k, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.ValidateApiKey(token)
	userIdStr := ""
	if err == nil {
		userIdStr = fmt.Sprintf("%d", k.UserId)
	}
	//if err != nil {
	// token不是api_key，如果有sse_url则直接代理
	if serverkey.SseUrl != "" {
		return handleThirdPartySSEProxy(c, ctx, key, serverkey, queryKey, pathToken, userIdStr)
	}
	// 没有sse_url还是返回原有错误
	//return c.String(http.StatusUnauthorized, fmt.Sprintf("Invalid token: %v", err))
	//}

	serverConfig := mcpserver.GetServerConfig(key)
	if serverConfig == nil {
		return c.String(http.StatusBadRequest, "Invalid server config")
	}

	// 解析数据库env_json，强制key大写并保留顺序
	dbEnv := map[string]string{}
	var envOrder []string
	if serverkey.EnvJson != "" {
		var rawEnv map[string]interface{}
		_ = json.Unmarshal([]byte(serverkey.EnvJson), &rawEnv)
		for k, v := range rawEnv {
			dbEnv[strings.ToUpper(k)] = fmt.Sprintf("%v", v)
		}
		// 用Decoder保留顺序
		decoder := json.NewDecoder(strings.NewReader(serverkey.EnvJson))
		_, _ = decoder.Token() // 跳过{
		for decoder.More() {
			tk, _ := decoder.Token()
			k := tk.(string)
			envOrder = append(envOrder, strings.ToUpper(k))
			decoder.Token() // 跳过值
		}
	}
	for k, v := range dbEnv {
		log.Printf("dbEnv key: %s, value: %s", k, v)
	}
	log.Printf("envOrder: %+v", envOrder)

	// 合并header，key不区分大小写
	headerEnv := map[string]string{}
	headerEnvStr := c.Request().Header.Get("X-MCP-ENV")
	if headerEnvStr != "" {
		_ = json.Unmarshal([]byte(headerEnvStr), &headerEnv)
	}
	normalizedHeader := map[string]string{}
	for k, v := range c.Request().Header {
		if len(v) > 0 {
			normalizedHeader[strings.ToUpper(k)] = v[0]
		}
	}

	// 获取key_param_name
	keyParamName := serverkey.KeyParamName
	if keyParamName == "" {
		keyParamName = "key"
	}
	keyParamName = strings.ToUpper(keyParamName)

	// 获取header和env_json里的key值
	headerKeyValue := normalizedHeader[keyParamName]
	envKeyValue := dbEnv[keyParamName]

	// 判断是否扣积分
	deductPoints := true
	if headerKeyValue != "" && headerKeyValue != envKeyValue {
		deductPoints = false // 用户自带key，不扣积分
	}
	// TODO: 在后续积分扣除逻辑处使用deductPoints变量
	_ = deductPoints // 避免linter错误，后续实际使用时可去掉

	// 判断是否需要走sse_url代理
	if serverkey.SseUrl != "" {
		return handleThirdPartySSEProxy(c, ctx, key, serverkey, queryKey, pathToken, userIdStr)
	}

	// 拼接，header优先（不区分大小写），dbEnv也用大写key
	envParts := []string{}
	for _, k := range envOrder {
		v := normalizedHeader[k]
		if v == "" {
			v = dbEnv[k]
		}
		if v == "" {
			v = "key"
		}
		envParts = append(envParts, fmt.Sprintf("%s=%s", k, v))
	}
	cmd := serverConfig.Command
	if len(envParts) > 0 {
		cmd = fmt.Sprintf("%s %s", strings.Join(envParts, " "), serverConfig.Command)
	}

	// 处理autoApprove参数
	autoApprove := c.Request().Header.Get("Autoapprove")
	if autoApprove != "" {
		cmd = fmt.Sprintf("%s --auto-approve=%s", cmd, autoApprove)
	}

	serverConfig.Command = cmd

	// 本地SSE分支
	writer, err := proxy.NewSSEWriter(c)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	sessionID := uuid.New().String()
	proxyInfo := &proxy.ProxyInfo{
		ServerKey:          key,
		ConnectionTime:     time.Now(),
		SessionID:          sessionID,
		ServerUUID:         serverConfig.ServerUUID,
		ServerConfigName:   serverConfig.ServerName,
		ServerShareProcess: serverConfig.ShareProcess,
		ServerType:         serverConfig.ServerType,
		ServerURL:          serverConfig.ServerURL,
		ServerCommand:      serverConfig.Command,
		ServerCommandHash:  serverConfig.CommandHash,
		UserID:             userIdStr,
		HeaderEnv:          normalizedHeader,
		DbEnv:              dbEnv,
		IP:                 getClientIP(c),
	}
	session := proxy.NewSSESession(writer, serverConfig, proxyInfo)

	// 新增：SSE进程复用逻辑，与mcp.go保持一致
	client, err := getOrCreateMCPClient(ctx, key, serverConfig, session)
	if err != nil {
		log.Printf("[SSE进程复用] 获取客户端失败: %v", err)
		// 不返回错误，因为SSE可能不需要立即创建客户端
	} else if client != nil {
		session.SetClient(client)
	}

	ctx.StoreSession(sessionID, session)
	defer ctx.DeleteSession(sessionID)

	// Setup heartbeat ticker
	heartbeatInterval := 30 * time.Second // adjust interval as needed
	heartbeatTicker := time.NewTicker(heartbeatInterval)
	defer heartbeatTicker.Stop()

	// Setup idle timeout
	idleTimeout := 5 * time.Minute // adjust timeout as needed
	idleTimer := time.NewTimer(idleTimeout)
	defer idleTimer.Stop()

	// Reset idle timer when activity occurs
	resetIdleTimer := func() {
		if !idleTimer.Stop() {
			<-idleTimer.C
		}
		idleTimer.Reset(idleTimeout)
	}

	go func() {
		for {
			select {
			case <-session.Done():
				return
			case <-req.Context().Done():
				return
			case <-heartbeatTicker.C:
				// Send heartbeat comment
				if err := writer.SendHeartbeat(); err != nil {
					session.Close()
					return
				}
			case <-idleTimer.C:
				// Close connection due to inactivity
				session.Close()
				return
			}
		}
	}()

	// response to client with endpoint url
	messagesUrl := fmt.Sprintf("/messages?sessionid=%s", sessionID)
	writer.SendEventData("endpoint", messagesUrl)

	// listen to messages
	for {
		select {
		case message := <-session.Messages():
			resetIdleTimer()
			if err := writer.SendMessage(message); err != nil {
				fmt.Printf("sse failed to send message to session %s: %v\n", sessionID, err)
				session.Close() // Close session on send error
				return nil      // Exit the handler
			}
		case <-session.Done():
			fmt.Printf("session %s closed \n", sessionID)
			return nil
		case <-req.Context().Done():
			fmt.Println("sse request done")
			session.Close()
			return nil
		}
	}
}
